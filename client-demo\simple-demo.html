<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic AI Order Processing - Simple Demo</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Heroicons CSS for professional icons -->
    <style>
        .icon {
            width: 1.25rem;
            height: 1.25rem;
            display: inline-block;
            vertical-align: middle;
        }

        .icon-lg {
            width: 2rem;
            height: 2rem;
        }

        .icon-xl {
            width: 2.5rem;
            height: 2.5rem;
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="demo-header">
        <div class="header-content">
            <div class="header-main">
                <div class="header-title">
                    <div class="header-icon">
                        <svg class="icon-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h1>Agentic AI Order Processing</h1>
                </div>
                <p class="header-subtitle">
                    Transform complex purchase orders into processed orders in <span class="highlight-time">15
                        seconds</span> with advanced AI automation
                </p>

                <div class="header-metrics">
                    <div class="metric-card">
                        <span class="metric-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </span>
                        <span class="metric-value">96%</span>
                        <span class="metric-label">Faster Processing</span>
                    </div>
                    <div class="metric-card">
                        <span class="metric-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </span>
                        <span class="metric-value">99.2%</span>
                        <span class="metric-label">Accuracy Rate</span>
                    </div>
                    <div class="metric-card">
                        <span class="metric-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </span>
                        <span class="metric-value">$82</span>
                        <span class="metric-label">Saved per Order</span>
                    </div>
                    <div class="metric-card">
                        <span class="metric-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </span>
                        <span class="metric-value">78%</span>
                        <span class="metric-label">Automation Rate</span>
                    </div>
                </div>
            </div>

            <div class="header-cta">
                <div class="roi-display">
                    <div class="roi-badge">ROI Impact</div>
                    <div class="roi-value">$320K</div>
                    <div class="roi-description">Annual cost savings<br>based on 4,000 orders/year</div>
                    <div class="live-indicator">
                        <div class="pulse-dot"></div>
                        <span>Live Demo</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Demo Area -->
    <main class="demo-container">
        <!-- Before/After Comparison -->
        <section class="comparison-section">
            <div class="section-header">
                <h2>The Transformation</h2>
                <p>See how AI revolutionizes order processing</p>
            </div>

            <div class="comparison-container">
                <div class="comparison-card before">
                    <div class="card-header">
                        <h3>
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #ef4444;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Manual Process (Before)
                        </h3>
                        <div class="inefficiency-badge">INEFFICIENT</div>
                    </div>
                    <div class="process-time">
                        <span class="time-number">2-4</span>
                        <span class="time-unit">Hours</span>
                    </div>
                    <div class="process-breakdown">
                        <div class="breakdown-item">
                            <span class="step-time">30 min</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                Manual email checking
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">45 min</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Print and review documents
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">60 min</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                Manual data entry
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">20 min</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                Phone calls for verification
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">25 min</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                Multiple system updates
                            </span>
                        </div>
                    </div>
                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <span class="cost-label">Labor Cost:</span>
                            <span class="cost-value">$75</span>
                        </div>
                        <div class="cost-item">
                            <span class="cost-label">Error Cost:</span>
                            <span class="cost-value">$10</span>
                        </div>
                        <div class="cost-total">Total: $85 per order</div>
                    </div>
                    <div class="problems">
                        <div class="problem-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #ef4444;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            15% error rate
                        </div>
                        <div class="problem-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #ef4444;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Customer delays
                        </div>
                        <div class="problem-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #ef4444;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Staff burnout
                        </div>
                    </div>
                </div>

                <div class="transformation-arrow">
                    <div class="arrow-container">
                        <div class="arrow-line"></div>
                        <div class="arrow-head">→</div>
                    </div>
                    <div class="ai-transformation-badge">
                        <span class="badge-text">AI TRANSFORMATION</span>
                        <div class="transformation-stats">
                            <div class="transform-stat">96% Faster</div>
                            <div class="transform-stat">97% Cheaper</div>
                        </div>
                    </div>
                </div>

                <div class="comparison-card after">
                    <div class="card-header">
                        <h3>
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #10b981;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            AI-Powered Process (After)
                        </h3>
                        <div class="efficiency-badge">OPTIMIZED</div>
                    </div>
                    <div class="process-time">
                        <span class="time-number">15</span>
                        <span class="time-unit">Seconds</span>
                    </div>
                    <div class="process-breakdown">
                        <div class="breakdown-item">
                            <span class="step-time">0.3s</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                AI email detection
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">1.2s</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                Intelligent classification
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">2.1s</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                AI data extraction
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">1.8s</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Smart validation
                            </span>
                        </div>
                        <div class="breakdown-item">
                            <span class="step-time">1.2s</span>
                            <span class="step-desc">
                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                Automated processing
                            </span>
                        </div>
                    </div>
                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <span class="cost-label">AI Processing:</span>
                            <span class="cost-value">$2</span>
                        </div>
                        <div class="cost-item">
                            <span class="cost-label">Oversight:</span>
                            <span class="cost-value">$1</span>
                        </div>
                        <div class="cost-total">Total: $3 per order</div>
                    </div>
                    <div class="benefits">
                        <div class="benefit-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #10b981;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            99.2% accuracy
                        </div>
                        <div class="benefit-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #10b981;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Instant processing
                        </div>
                        <div class="benefit-item">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                style="color: #10b981;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Happy customers
                        </div>
                    </div>
                </div>
            </div>

            <div class="savings-highlight">
                <div class="savings-card">
                    <div class="savings-icon">
                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <div class="savings-content">
                        <div class="savings-title">Cost Savings Per Order</div>
                        <div class="savings-amount">$82 <span class="savings-percent">(97% reduction)</span></div>
                    </div>
                </div>
                <div class="savings-card">
                    <div class="savings-icon">
                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="savings-content">
                        <div class="savings-title">Time Savings Per Order</div>
                        <div class="savings-amount">3.75 hours <span class="savings-percent">(96% reduction)</span>
                        </div>
                    </div>
                </div>
                <div class="savings-card">
                    <div class="savings-icon">
                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                    <div class="savings-content">
                        <div class="savings-title">Annual Impact</div>
                        <div class="savings-amount">$320K <span class="savings-percent">saved yearly</span></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Live Demo Flow -->
        <section class="flow-section">
            <div class="flow-header">
                <h2>
                    <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Live Demo: Watch AI in Action
                </h2>
                <p>See how our AI processes a real $13K order from TechCorp Solutions</p>
                <div class="demo-controls">
                    <button id="startDemo" class="demo-btn primary">
                        <span class="btn-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </span>
                        <span class="btn-text">Start Live Demo</span>
                    </button>
                    <button id="resetDemo" class="demo-btn secondary">
                        <span class="btn-icon">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </span>
                        <span class="btn-text">Reset</span>
                    </button>
                    <div class="demo-info">
                        <span class="info-text">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            Tip: Click individual "Execute" buttons to advance through each phase
                        </span>
                    </div>
                </div>
            </div>

            <!-- Progress Indicator -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text">
                    <span id="progressStatus">Ready to start AI-powered order processing demonstration</span>
                    <span id="progressTime">Manual control mode - Click buttons to advance</span>
                </div>
            </div>

            <!-- Flow Steps -->
            <div class="flow-container">
                <!-- Step 1: Email Arrives -->
                <div class="flow-step" id="step1">
                    <div class="step-number">1</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>Email Arrives</h4>
                            <div class="step-timing">45 seconds</div>
                        </div>
                        <p class="step-description">AI detects, analyzes, and classifies incoming purchase order email
                        </p>
                        <div class="step-details">
                            <div class="email-preview">
                                <div class="email-window">
                                    <div class="email-toolbar">
                                        <div class="email-buttons">
                                            <span class="btn-red"></span>
                                            <span class="btn-yellow"></span>
                                            <span class="btn-green"></span>
                                        </div>
                                        <div class="email-title">Inbox</div>
                                    </div>
                                    <div class="email-content">
                                        <div class="email-header">
                                            <div class="email-from">
                                                <strong>From:</strong> Sarah Johnson &lt;<EMAIL>&gt;
                                            </div>
                                            <div class="email-subject">
                                                <strong>Subject:</strong>
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                    style="color: #ef4444;">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                                </svg>
                                                URGENT - Purchase Order #PO-TC-2025-001
                                            </div>
                                            <div class="email-priority">
                                                <span class="priority-badge">HIGH PRIORITY</span>
                                            </div>
                                        </div>
                                        <div class="email-attachment">
                                            <div class="attachment-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                                </svg>
                                            </div>
                                            <div class="attachment-info">
                                                <div class="attachment-name">PO-TC-2025-001.pdf</div>
                                                <div class="attachment-size">245.76 KB</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="0">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Email Processing</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>

                <!-- Step 2: AI Analysis -->
                <div class="flow-step" id="step2">
                    <div class="step-number">2</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>AI Analyzes Email</h4>
                            <div class="step-timing">90 seconds</div>
                        </div>
                        <p class="step-description">Advanced AI performs comprehensive document analysis,
                            classification, and content extraction</p>
                        <div class="step-details">
                            <div class="ai-analysis">
                                <div class="analysis-header">
                                    <div class="ai-brain">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                    </div>
                                    <div class="analysis-title">AI Analysis Results</div>
                                    <div class="confidence-score">95% Confidence</div>
                                </div>
                                <div class="analysis-grid">
                                    <div class="analysis-item">
                                        <div class="analysis-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                            </svg>
                                        </div>
                                        <div class="analysis-content">
                                            <span class="label">Document Type:</span>
                                            <span class="value highlight">Purchase Order</span>
                                        </div>
                                    </div>
                                    <div class="analysis-item">
                                        <div class="analysis-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                style="color: #ef4444;">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                        </div>
                                        <div class="analysis-content">
                                            <span class="label">Urgency Level:</span>
                                            <span class="value urgent">HIGH PRIORITY</span>
                                        </div>
                                    </div>
                                    <div class="analysis-item">
                                        <div class="analysis-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <div class="analysis-content">
                                            <span class="label">Customer Status:</span>
                                            <span class="value">Premium (18 months)</span>
                                        </div>
                                    </div>
                                    <div class="analysis-item">
                                        <div class="analysis-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <div class="analysis-content">
                                            <span class="label">Sentiment:</span>
                                            <span class="value">Professional/Urgent</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced AI Features -->
                                <div class="advanced-analysis">
                                    <div class="analysis-section">
                                        <h5>
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                            </svg>
                                            Behavioral Pattern Analysis
                                        </h5>
                                        <div class="pattern-grid">
                                            <div class="pattern-item">
                                                <span class="pattern-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                    </svg>
                                                </span>
                                                <span class="pattern-label">Order Pattern:</span>
                                                <span class="pattern-value stable">Quarterly Consistent</span>
                                            </div>
                                            <div class="pattern-item">
                                                <span class="pattern-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                    </svg>
                                                </span>
                                                <span class="pattern-label">Fraud Risk:</span>
                                                <span class="pattern-value safe">Low (2/100)</span>
                                            </div>
                                            <div class="pattern-item">
                                                <span class="pattern-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                                    </svg>
                                                </span>
                                                <span class="pattern-label">Order Size:</span>
                                                <span class="pattern-value normal">Within Normal Range</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="analysis-section">
                                        <h5>
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Market Intelligence
                                        </h5>
                                        <div class="market-grid">
                                            <div class="market-item">
                                                <span class="market-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                                    </svg>
                                                </span>
                                                <span class="market-label">Our Price vs Market:</span>
                                                <span class="market-value competitive">5% Below Average</span>
                                            </div>
                                            <div class="market-item">
                                                <span class="market-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </span>
                                                <span class="market-label">Competitive Position:</span>
                                                <span class="market-value strong">Strong</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ai-thinking">
                                    <div class="thinking-indicator">
                                        <div class="thinking-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                        <span class="thinking-text">AI is processing...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="1">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute AI Analysis</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">⏳</div>
                        <div class="status-text">Analyzing</div>
                    </div>
                </div>

                <!-- Step 3: Document Processing -->
                <div class="flow-step" id="step3">
                    <div class="step-number">3</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>AI Extracts Order Data</h4>
                            <div class="step-timing">75 seconds</div>
                        </div>
                        <p class="step-description">AI performs detailed PDF analysis and extracts all order information
                            with high precision,
                            including line items, pricing, and delivery requirements
                        </p>
                        <div class="step-details">
                            <div class="extraction-preview">
                                <div class="extraction-header">
                                    <div class="extraction-icon">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <div class="extraction-title">Data Extraction Results</div>
                                    <div class="extraction-confidence">92% Confidence</div>
                                </div>
                                <div class="extracted-data">
                                    <div class="data-item">
                                        <div class="data-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                        </div>
                                        <div class="data-content">
                                            <span class="label">Order Value:</span>
                                            <span class="value highlight">$12,999.65</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                            </svg>
                                        </div>
                                        <div class="data-content">
                                            <span class="label">Items:</span>
                                            <span class="value">25 Ergonomic Chairs + 10 Standing Desks</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                                            </svg>
                                        </div>
                                        <div class="data-content">
                                            <span class="label">Delivery Date:</span>
                                            <span class="value urgent">July 25, 2025 (CRITICAL)</span>
                                        </div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                        </div>
                                        <div class="data-content">
                                            <span class="label">Customer:</span>
                                            <span class="value">TechCorp Solutions Inc.</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contract Terms Analysis -->
                                <div class="contract-analysis">
                                    <h5>
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                        </svg>
                                        Contract Terms Analysis
                                    </h5>
                                    <div class="contract-grid">
                                        <div class="contract-item">
                                            <span class="contract-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-3m3 3l3-3" />
                                                </svg>
                                            </span>
                                            <span class="contract-label">Payment Terms:</span>
                                            <span class="contract-value">Net 30</span>
                                        </div>
                                        <div class="contract-item">
                                            <span class="contract-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                                </svg>
                                            </span>
                                            <span class="contract-label">SLA Requirements:</span>
                                            <span class="contract-value critical">Delivery by July 25th
                                                (Critical)</span>
                                        </div>
                                        <div class="contract-item">
                                            <span class="contract-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </span>
                                            <span class="contract-label">Special Terms:</span>
                                            <span class="contract-value">Loading dock delivery only</span>
                                        </div>
                                        <div class="contract-item">
                                            <span class="contract-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                                </svg>
                                            </span>
                                            <span class="contract-label">Penalty Clauses:</span>
                                            <span class="contract-value safe">None specified</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="extraction-progress">
                                    <div class="progress-item">
                                        <span class="progress-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>PDF parsed successfully</span>
                                    </div>
                                    <div class="progress-item">
                                        <span class="progress-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>Line items extracted</span>
                                    </div>
                                    <div class="progress-item">
                                        <span class="progress-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>Contract terms identified</span>
                                    </div>
                                    <div class="progress-item">
                                        <span class="progress-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>SLA requirements flagged</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="2">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Data Extraction</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>

                <!-- Step 4: Smart Validation -->
                <div class="flow-step" id="step4">
                    <div class="step-number">4</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>AI Validates Everything</h4>
                            <div class="step-timing">45 seconds</div>
                        </div>
                        <p class="step-description">Comprehensive validation of pricing, inventory availability, credit
                            status, compliance rules,
                            and business logic verification</p>
                        <div class="step-details">
                            <div class="validation-preview">
                                <div class="validation-header">
                                    <div class="validation-icon">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                    <div class="validation-title">Validation Results</div>
                                    <div class="validation-score">All Checks Passed</div>
                                </div>
                                <div class="validation-grid">
                                    <div class="validation-item">
                                        <div class="validation-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="validation-content">
                                            <div class="validation-label">Pricing Verification</div>
                                            <div class="validation-detail">Market rates confirmed</div>
                                        </div>
                                    </div>
                                    <div class="validation-item">
                                        <div class="validation-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="validation-content">
                                            <div class="validation-label">Inventory Check</div>
                                            <div class="validation-detail">All items in stock</div>
                                        </div>
                                    </div>
                                    <div class="validation-item">
                                        <div class="validation-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="validation-content">
                                            <div class="validation-label">Credit Assessment</div>
                                            <div class="validation-detail">Low risk (15/100)</div>
                                        </div>
                                    </div>
                                    <div class="validation-item">
                                        <div class="validation-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="validation-content">
                                            <div class="validation-label">Compliance Check</div>
                                            <div class="validation-detail">All regulations met</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Supply Chain Optimization -->
                                <div class="optimization-section">
                                    <h5>
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                                        </svg>
                                        Supply Chain Optimization
                                    </h5>
                                    <div class="optimization-grid">
                                        <div class="optimization-item">
                                            <span class="opt-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                                </svg>
                                            </span>
                                            <span class="opt-label">Optimal Warehouse:</span>
                                            <span class="opt-value recommended">Aurora, IL (1 day faster)</span>
                                        </div>
                                        <div class="optimization-item">
                                            <span class="opt-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </span>
                                            <span class="opt-label">Carbon Footprint:</span>
                                            <span class="opt-value eco">12.5 kg CO₂ (Low impact)</span>
                                        </div>
                                        <div class="optimization-item">
                                            <span class="opt-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                                </svg>
                                            </span>
                                            <span class="opt-label">Consolidation:</span>
                                            <span class="opt-value savings">$45 savings available</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Margin Optimization -->
                                <div class="margin-section">
                                    <h5>
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                        Margin Optimization
                                    </h5>
                                    <div class="margin-opportunities">
                                        <div class="opportunity-item">
                                            <span class="opp-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </span>
                                            <span class="opp-text">Upsell Opportunity: Chair mats + desk lamps (+$125,
                                                +15% margin)</span>
                                        </div>
                                        <div class="opportunity-item">
                                            <span class="opp-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                </svg>
                                            </span>
                                            <span class="opp-text">Extended Warranty: 3-year protection (+$195, +85%
                                                margin)</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="validation-summary">
                                    <div class="summary-badge success">
                                        <span class="badge-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span class="badge-text">Ready for Processing</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="3">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Validation</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>

                <!-- Step 5: Credit Assessment -->
                <div class="flow-step" id="step5">
                    <div class="step-number">5</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>AI Credit Assessment</h4>
                            <div class="step-timing">60 seconds</div>
                        </div>
                        <p class="step-description">AI performs comprehensive credit risk analysis, payment history
                            review, and automated credit decision making</p>
                        <div class="step-details">
                            <div class="credit-assessment">
                                <div class="credit-header">
                                    <div class="credit-icon">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                    <div class="credit-title">Credit Risk Analysis</div>
                                    <div class="credit-score">742 Credit Score</div>
                                </div>

                                <div class="credit-metrics">
                                    <div class="metric-item">
                                        <div class="metric-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                        </div>
                                        <div class="metric-content">
                                            <span class="metric-label">Payment History:</span>
                                            <span class="metric-value excellent">98.5% On-Time</span>
                                        </div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                            </svg>
                                        </div>
                                        <div class="metric-content">
                                            <span class="metric-label">Credit Utilization:</span>
                                            <span class="metric-value good">23% ($46K of $200K)</span>
                                        </div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                            </svg>
                                        </div>
                                        <div class="metric-content">
                                            <span class="metric-label">Risk Score:</span>
                                            <span class="metric-value low-risk">15/100 (Low Risk)</span>
                                        </div>
                                    </div>
                                    <div class="metric-item">
                                        <div class="metric-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="metric-content">
                                            <span class="metric-label">Available Credit:</span>
                                            <span class="metric-value approved">$50K Limit Available</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Business Value Section -->
                                <div class="business-value-section">
                                    <h5>
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                        Business Value & ROI Impact
                                    </h5>
                                    <div class="value-grid">
                                        <div class="value-item">
                                            <span class="value-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                </svg>
                                            </span>
                                            <span class="value-label">Processing Speed:</span>
                                            <span class="value-metric">45 minutes saved vs manual review</span>
                                        </div>
                                        <div class="value-item">
                                            <span class="value-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                </svg>
                                            </span>
                                            <span class="value-label">Risk Reduction:</span>
                                            <span class="value-metric">85% reduction in bad debt exposure</span>
                                        </div>
                                        <div class="value-item">
                                            <span class="value-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                                </svg>
                                            </span>
                                            <span class="value-label">Cost Savings:</span>
                                            <span class="value-metric">$125 saved per credit assessment</span>
                                        </div>
                                        <div class="value-item">
                                            <span class="value-icon">
                                                <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                                </svg>
                                            </span>
                                            <span class="value-label">Accuracy:</span>
                                            <span class="value-metric">99.2% vs 87% manual accuracy</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="credit-decision">
                                    <div class="decision-badge approved">
                                        <span class="decision-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span class="decision-text">CREDIT APPROVED - Proceed to Fulfillment</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="4">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Credit Assessment</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">⏳</div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>

                <!-- Step 6: Fulfillment Coordination -->
                <div class="flow-step" id="step6">
                    <div class="step-number">6</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>AI Fulfillment Coordination</h4>
                            <div class="step-timing">75 seconds</div>
                        </div>
                        <p class="step-description">AI coordinates warehouse operations, logistics providers, and
                            delivery optimization for maximum efficiency</p>
                        <div class="step-details">
                            <div class="fulfillment-coordination">
                                <div class="fulfillment-header">
                                    <div class="fulfillment-icon">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div class="fulfillment-title">Intelligent Fulfillment Orchestration</div>
                                    <div class="fulfillment-status">Optimizing Operations</div>
                                </div>

                                <div class="coordination-sections">
                                    <div class="coordination-section">
                                        <h5>
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                            Warehouse System Integration
                                        </h5>
                                        <div class="warehouse-grid">
                                            <div class="warehouse-item">
                                                <span class="warehouse-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                                    </svg>
                                                </span>
                                                <span class="warehouse-label">Selected Warehouse:</span>
                                                <span class="warehouse-value optimal">Aurora, IL (1 day faster)</span>
                                            </div>
                                            <div class="warehouse-item">
                                                <span class="warehouse-icon">
                                                    <svg class="icon" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                                    </svg>
                                                </span>
                                                <span class="warehouse-label">Inventory Status:</span>
                                                <span class="warehouse-value available">All items reserved</span>
                                            </div>
                                            <div class="warehouse-item">
                                                <span class="warehouse-icon">⏰</span>
                                                <span class="warehouse-label">Pick Schedule:</span>
                                                <span class="warehouse-value scheduled">Tomorrow 8:00 AM</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="coordination-section">
                                        <h5>
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Logistics Provider Coordination
                                        </h5>
                                        <div class="logistics-grid">
                                            <div class="logistics-item">
                                                <span class="logistics-icon">🚛</span>
                                                <span class="logistics-label">Carrier Selected:</span>
                                                <span class="logistics-value selected">FedEx Freight (Best rate +
                                                    timing)</span>
                                            </div>
                                            <div class="logistics-item">
                                                <span class="logistics-icon">📅</span>
                                                <span class="logistics-label">Delivery Window:</span>
                                                <span class="logistics-value confirmed">July 24th, 10:00 AM - 2:00
                                                    PM</span>
                                            </div>
                                            <div class="logistics-item">
                                                <span class="logistics-icon">🌱</span>
                                                <span class="logistics-label">Carbon Impact:</span>
                                                <span class="logistics-value eco">12.5 kg CO₂ (Low impact route)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ROI Metrics Section -->
                                <div class="roi-metrics-section">
                                    <h5>
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                        Operational Efficiency & ROI
                                    </h5>
                                    <div class="roi-grid">
                                        <div class="roi-item">
                                            <span class="roi-icon">⚡</span>
                                            <span class="roi-label">Fulfillment Speed:</span>
                                            <span class="roi-metric">65% faster than manual coordination</span>
                                        </div>
                                        <div class="roi-item">
                                            <span class="roi-icon">💰</span>
                                            <span class="roi-label">Cost Optimization:</span>
                                            <span class="roi-metric">$45 shipping savings per order</span>
                                        </div>
                                        <div class="roi-item">
                                            <span class="roi-icon">📊</span>
                                            <span class="roi-label">Route Efficiency:</span>
                                            <span class="roi-metric">3 stops consolidated</span>
                                        </div>
                                        <div class="roi-item">
                                            <span class="roi-icon">🎯</span>
                                            <span class="roi-label">Delivery Accuracy:</span>
                                            <span class="roi-metric">99.8% on-time delivery rate</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="tracking-setup">
                                    <div class="tracking-badge">
                                        <span class="tracking-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span class="tracking-text">📱 Real-time tracking portal activated for
                                            customer</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="5">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Fulfillment Coordination</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">⏳</div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>

                <!-- Step 7: Order Created -->
                <div class="flow-step" id="step7">
                    <div class="step-number">7</div>
                    <div class="step-icon-container">
                        <div class="step-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="step-pulse"></div>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <h4>Order Created & Customer Notified</h4>
                            <div class="step-timing">15 seconds</div>
                        </div>
                        <p class="step-description">Final order creation, inventory updates, and comprehensive customer
                            notification</p>
                        <div class="step-details">
                            <div class="completion-preview">
                                <div class="completion-header">
                                    <div class="completion-icon">
                                        <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="completion-title">Order Successfully Created</div>
                                    <div class="completion-badge">COMPLETED</div>
                                </div>
                                <div class="order-summary">
                                    <div class="summary-item">
                                        <div class="summary-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                            </svg>
                                        </div>
                                        <div class="summary-content">
                                            <span class="label">Order Number:</span>
                                            <span class="value highlight">ORD-2025-001234</span>
                                        </div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="summary-content">
                                            <span class="label">Status:</span>
                                            <span class="value success">CONFIRMED</span>
                                        </div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div class="summary-content">
                                            <span class="label">Customer Notified:</span>
                                            <span class="value success">Email sent to Sarah Johnson</span>
                                        </div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="summary-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                            </svg>
                                        </div>
                                        <div class="summary-content">
                                            <span class="label">ERP Updated:</span>
                                            <span class="value success">SAP system synchronized</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="completion-actions">
                                    <div class="action-item">
                                        <span class="action-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>Order created in system</span>
                                    </div>
                                    <div class="action-item">
                                        <span class="action-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>Confirmation email sent</span>
                                    </div>
                                    <div class="action-item">
                                        <span class="action-check">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span>Warehouse notified</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Execute Button -->
                        <div class="step-execute-container" style="display: none;">
                            <button class="step-execute-btn" data-step="6">
                                <span class="execute-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </span>
                                <span class="execute-text">Execute Order Creation</span>
                            </button>
                        </div>
                    </div>
                    <div class="step-status">
                        <div class="status-indicator">
                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="status-text">Waiting</div>
                    </div>
                </div>
            </div>

            <!-- Results Summary -->
            <div class="results-summary" id="results">
                <h3>
                    <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Order Processed Successfully!
                </h3>
                <div class="summary-stats">
                    <div class="stat">
                        <div class="stat-number">4.5min</div>
                        <div class="stat-label">AI Processing Time</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">$247</div>
                        <div class="stat-label">Labor Cost Saved</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Human Errors</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">99.8%</div>
                        <div class="stat-label">Accuracy Rate</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Human-in-the-Loop Control Points -->
        <section class="control-points-section">
            <div class="section-header">
                <h2>
                    <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    Human-in-the-Loop Control Points
                </h2>
                <p>AI handles routine orders automatically, but escalates complex cases for human expertise</p>
            </div>

            <div class="control-tabs">
                <button class="control-tab active" data-tab="exception-queue">Exception Review Queue</button>
                <button class="control-tab" data-tab="approval-workflow">Approval Workflow</button>
                <button class="control-tab" data-tab="regulation-monitoring">Regulation Monitoring</button>
            </div>

            <!-- Exception Review Queue -->
            <div class="control-content active" id="exception-queue">
                <div class="exception-dashboard">
                    <div class="dashboard-header">
                        <div class="dashboard-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <div class="dashboard-title">Exception Review Queue</div>
                        <div class="dashboard-badge">2 Items Pending</div>
                    </div>

                    <div class="exception-items">
                        <div class="exception-item">
                            <div class="exception-priority high">HIGH</div>
                            <div class="exception-content">
                                <div class="exception-header">
                                    <span class="exception-id">EXC-2025-001</span>
                                    <span class="exception-type">Data Quality Issue</span>
                                    <span class="exception-time">2 min ago</span>
                                </div>
                                <div class="exception-description">
                                    Order PO-GM-2025-0045: Missing customer phone number and incomplete billing address
                                </div>
                                <div class="ai-recommendation">
                                    <div class="rec-header">
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        AI Recommendation:
                                    </div>
                                    <div class="rec-text">Contact customer for missing info. Standard pricing of
                                        $45.50/unit suggested based on history.</div>
                                    <div class="rec-confidence">Confidence: 85%</div>
                                </div>
                                <div class="exception-actions">
                                    <button class="action-btn primary">Apply AI Suggestion</button>
                                    <button class="action-btn secondary">Manual Review</button>
                                    <button class="action-btn tertiary">Contact Customer</button>
                                </div>
                            </div>
                        </div>

                        <div class="exception-item">
                            <div class="exception-priority medium">MEDIUM</div>
                            <div class="exception-content">
                                <div class="exception-header">
                                    <span class="exception-id">EXC-2025-002</span>
                                    <span class="exception-type">Pricing Exception</span>
                                    <span class="exception-time">5 min ago</span>
                                </div>
                                <div class="exception-description">
                                    Order PO-LM-2025-009: Requested discount exceeds approval threshold (18.3% vs 15%
                                    max)
                                </div>
                                <div class="ai-recommendation">
                                    <div class="rec-header">
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        AI Recommendation:
                                    </div>
                                    <div class="rec-text">High-value customer ($185K lifetime value). Recommend approval
                                        with sales manager override.</div>
                                    <div class="rec-confidence">Confidence: 92%</div>
                                </div>
                                <div class="exception-actions">
                                    <button class="action-btn primary">Escalate to Manager</button>
                                    <button class="action-btn secondary">Counter-offer 15%</button>
                                    <button class="action-btn tertiary">Reject</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approval Workflow -->
            <div class="control-content" id="approval-workflow">
                <div class="approval-dashboard">
                    <div class="dashboard-header">
                        <div class="dashboard-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="dashboard-title">Management Approval Workflow</div>
                        <div class="dashboard-badge">1 Pending Approval</div>
                    </div>

                    <div class="approval-item">
                        <div class="approval-header">
                            <div class="approval-type">Credit Override Request</div>
                            <div class="approval-amount">$25,000 Order</div>
                            <div class="approval-urgency">URGENT</div>
                        </div>

                        <div class="approval-details">
                            <div class="detail-section">
                                <h4>Order Information</h4>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <span class="detail-label">Customer:</span>
                                        <span class="detail-value">Large Manufacturing Corp</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Current Exposure:</span>
                                        <span class="detail-value">$18,500</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Credit Limit:</span>
                                        <span class="detail-value">$20,000</span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Requested Override:</span>
                                        <span class="detail-value highlight">$23,500</span>
                                    </div>
                                </div>
                            </div>

                            <div class="ai-risk-assessment">
                                <h4>
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    AI Risk Assessment
                                </h4>
                                <div class="risk-score">
                                    <div class="score-circle medium-risk">
                                        <span class="score-number">78</span>
                                        <span class="score-label">Risk Score</span>
                                    </div>
                                    <div class="risk-factors">
                                        <div class="risk-factor">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                style="color: #f59e0b;">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                            Recent payment delays (3 in 12 months)
                                        </div>
                                        <div class="risk-factor">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                style="color: #f59e0b;">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                            Order 150% larger than average
                                        </div>
                                        <div class="risk-factor">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                style="color: #10b981;">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            $200K annual volume customer
                                        </div>
                                        <div class="risk-factor">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                                style="color: #10b981;">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            18-month relationship
                                        </div>
                                    </div>
                                </div>

                                <div class="ai-recommendation-box">
                                    <div class="rec-header">
                                        <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            style="color: #f59e0b;">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        AI Recommendation: CONDITIONAL APPROVAL
                                    </div>
                                    <div class="rec-conditions">
                                        <div class="condition">• Require 50% prepayment ($12,500)</div>
                                        <div class="condition">• Reduce payment terms to Net 15</div>
                                        <div class="condition">• Set up payment monitoring alerts</div>
                                    </div>
                                    <div class="rec-justification">
                                        <strong>Business Justification:</strong> Strategic customer with temporary cash
                                        flow issues.
                                        Prepayment significantly reduces risk while maintaining relationship.
                                    </div>
                                </div>
                            </div>

                            <div class="approval-actions">
                                <button class="action-btn approve">Approve with Conditions</button>
                                <button class="action-btn reject">Reject Override</button>
                                <button class="action-btn modify">Modify Terms</button>
                                <button class="action-btn escalate">Escalate to VP</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dynamic Regulation Monitoring -->
            <div class="control-content" id="regulation-monitoring">
                <div class="regulation-dashboard">
                    <div class="dashboard-header">
                        <div class="dashboard-icon">
                            <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-3m3 3l3-3" />
                            </svg>
                        </div>
                        <div class="dashboard-title">Dynamic Regulation Monitoring</div>
                        <div class="dashboard-badge">3 Active Alerts</div>
                    </div>

                    <div class="regulation-alerts">
                        <div class="alert-item critical">
                            <div class="alert-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    style="color: #ef4444;">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="alert-content">
                                <div class="alert-header">
                                    <span class="alert-title">New Export Restrictions - China</span>
                                    <span class="alert-date">Effective: July 15, 2025</span>
                                </div>
                                <div class="alert-description">
                                    Additional technology categories added to export restriction list.
                                    All China-bound orders require immediate review.
                                </div>
                                <div class="alert-impact">
                                    <strong>Impact:</strong> 2 pending orders affected, potential blocking
                                </div>
                                <div class="alert-actions">
                                    <button class="action-btn urgent">Review Affected Orders</button>
                                    <button class="action-btn secondary">Update Compliance Rules</button>
                                </div>
                            </div>
                        </div>

                        <div class="alert-item warning">
                            <div class="alert-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    style="color: #f59e0b;">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="alert-content">
                                <div class="alert-header">
                                    <span class="alert-title">Upcoming: CPSC Furniture Standards</span>
                                    <span class="alert-date">Effective: September 1, 2025</span>
                                </div>
                                <div class="alert-description">
                                    Updated safety standards for office furniture manufacturing.
                                    Preparation time: 45 days remaining.
                                </div>
                                <div class="alert-impact">
                                    <strong>Impact:</strong> Office furniture categories, certification requirements
                                </div>
                                <div class="alert-actions">
                                    <button class="action-btn primary">Prepare Compliance Plan</button>
                                    <button class="action-btn secondary">Notify Suppliers</button>
                                </div>
                            </div>
                        </div>

                        <div class="alert-item info">
                            <div class="alert-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    style="color: #3b82f6;">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="alert-content">
                                <div class="alert-header">
                                    <span class="alert-title">Tax Rate Update - Illinois</span>
                                    <span class="alert-date">Effective: August 1, 2025</span>
                                </div>
                                <div class="alert-description">
                                    Sales tax rate increase from 8.0% to 8.25% for Cook County.
                                </div>
                                <div class="alert-impact">
                                    <strong>Impact:</strong> Pricing calculations, customer notifications
                                </div>
                                <div class="alert-actions">
                                    <button class="action-btn primary">Update Tax Engine</button>
                                    <button class="action-btn secondary">Notify Customers</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analytics & Continuous Improvement Dashboard -->
        <section class="analytics-section">
            <div class="section-header">
                <h2>
                    <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Analytics & Continuous Improvement
                </h2>
                <p>Real-time performance tracking and AI-powered optimization recommendations</p>
            </div>

            <div class="analytics-dashboard">
                <!-- ROI Metrics -->
                <div class="analytics-row">
                    <div class="analytics-card roi-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                            <div class="card-title">ROI Analysis</div>
                            <div class="card-period">This Week</div>
                        </div>
                        <div class="roi-metrics">
                            <div class="roi-item">
                                <div class="roi-label">Weekly Savings</div>
                                <div class="roi-value">$5,000</div>
                                <div class="roi-breakdown">
                                    <div class="breakdown-item">Labor: $2,850</div>
                                    <div class="breakdown-item">Errors: $1,200</div>
                                    <div class="breakdown-item">Efficiency: $950</div>
                                </div>
                            </div>
                            <div class="roi-item">
                                <div class="roi-label">Revenue Impact</div>
                                <div class="roi-value">+$4,450</div>
                                <div class="roi-breakdown">
                                    <div class="breakdown-item">Upsells: $1,250</div>
                                    <div class="breakdown-item">Margins: $800</div>
                                    <div class="breakdown-item">Retention: $2,400</div>
                                </div>
                            </div>
                            <div class="roi-summary">
                                <div class="summary-label">Current ROI</div>
                                <div class="summary-value highlight">234%</div>
                                <div class="summary-note">Payback achieved in 7.2 months</div>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-card performance-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <div class="card-title">Performance Metrics</div>
                            <div class="card-period">Last 7 Days</div>
                        </div>
                        <div class="performance-metrics">
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                    </svg>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">Orders Processed</div>
                                    <div class="metric-value">47</div>
                                    <div class="metric-change positive">+12% vs last week</div>
                                </div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">Avg Processing Time</div>
                                    <div class="metric-value">14.5s</div>
                                    <div class="metric-change positive">-2.1s improvement</div>
                                </div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">AI Automation Rate</div>
                                    <div class="metric-value">78%</div>
                                    <div class="metric-change positive">+5% vs last week</div>
                                </div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-icon">
                                    <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-label">Accuracy Score</div>
                                    <div class="metric-value">94%</div>
                                    <div class="metric-change positive">+2% improvement</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Optimization Recommendations -->
                <div class="analytics-row">
                    <div class="analytics-card optimization-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <div class="card-title">AI Optimization Recommendations</div>
                            <div class="card-badge">3 Active</div>
                        </div>
                        <div class="optimization-recommendations">
                            <div class="recommendation-item high-impact">
                                <div class="rec-priority">HIGH IMPACT</div>
                                <div class="rec-content">
                                    <div class="rec-title">Implement Parallel Credit Checks</div>
                                    <div class="rec-description">
                                        Credit checks currently take 3.2 minutes average. Parallel processing for
                                        low-risk customers could reduce this by 25%.
                                    </div>
                                    <div class="rec-impact">
                                        <span class="impact-label">Expected Benefit:</span>
                                        <span class="impact-value">25% faster processing, $850/week savings</span>
                                    </div>
                                    <div class="rec-actions">
                                        <button class="rec-btn primary">Implement</button>
                                        <button class="rec-btn secondary">Learn More</button>
                                    </div>
                                </div>
                            </div>

                            <div class="recommendation-item medium-impact">
                                <div class="rec-priority">MEDIUM IMPACT</div>
                                <div class="rec-content">
                                    <div class="rec-title">Customer Data Enrichment Rule</div>
                                    <div class="rec-description">
                                        Global Manufacturing Corp frequently sends orders with missing phone numbers.
                                        Auto-populate from CRM history.
                                    </div>
                                    <div class="rec-impact">
                                        <span class="impact-label">Expected Benefit:</span>
                                        <span class="impact-value">15% fewer data quality exceptions</span>
                                    </div>
                                    <div class="rec-actions">
                                        <button class="rec-btn primary">Create Rule</button>
                                        <button class="rec-btn secondary">Review Pattern</button>
                                    </div>
                                </div>
                            </div>

                            <div class="recommendation-item low-impact">
                                <div class="rec-priority">OPTIMIZATION</div>
                                <div class="rec-content">
                                    <div class="rec-title">Enhanced Upsell Detection</div>
                                    <div class="rec-description">
                                        Office furniture orders show 85% correlation with accessory needs. Enhance
                                        bundle detection algorithms.
                                    </div>
                                    <div class="rec-impact">
                                        <span class="impact-label">Expected Benefit:</span>
                                        <span class="impact-value">12% increase in upsell opportunities</span>
                                    </div>
                                    <div class="rec-actions">
                                        <button class="rec-btn primary">Enhance Algorithm</button>
                                        <button class="rec-btn secondary">View Analysis</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Learning & Adaptation -->
                <div class="analytics-row">
                    <div class="analytics-card learning-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                            </div>
                            <div class="card-title">AI Learning & Adaptation</div>
                            <div class="card-status">Continuously Improving</div>
                        </div>
                        <div class="learning-metrics">
                            <div class="learning-section">
                                <h4>Recent Improvements</h4>
                                <div class="improvement-list">
                                    <div class="improvement-item">
                                        <span class="improvement-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                            </svg>
                                        </span>
                                        <span class="improvement-text">Document extraction accuracy improved from 89% to
                                            94% (last 30 days)</span>
                                    </div>
                                    <div class="improvement-item">
                                        <span class="improvement-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        <span class="improvement-text">Pricing validation false positives reduced by
                                            40%</span>
                                    </div>
                                    <div class="improvement-item">
                                        <span class="improvement-icon">
                                            <svg class="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </span>
                                        <span class="improvement-text">New fraud detection patterns learned from 3 edge
                                            cases</span>
                                    </div>
                                </div>
                            </div>

                            <div class="learning-section">
                                <h4>Training Data Quality</h4>
                                <div class="training-stats">
                                    <div class="training-stat">
                                        <div class="stat-label">Documents Processed</div>
                                        <div class="stat-value">2,847</div>
                                    </div>
                                    <div class="training-stat">
                                        <div class="stat-label">Patterns Identified</div>
                                        <div class="stat-value">156</div>
                                    </div>
                                    <div class="training-stat">
                                        <div class="stat-label">Model Updates</div>
                                        <div class="stat-value">12</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Business Impact -->
        <section class="impact-section">
            <h2>Business Impact</h2>
            <div class="impact-grid">
                <div class="impact-card">
                    <div class="impact-icon">
                        <svg class="icon-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <div class="impact-title">Speed</div>
                    <div class="impact-value">96% Faster</div>
                    <div class="impact-desc">15 seconds vs 2-4 hours</div>
                </div>
                <div class="impact-card">
                    <div class="impact-icon">
                        <svg class="icon-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <div class="impact-title">Cost Savings</div>
                    <div class="impact-value">$320K/Year</div>
                    <div class="impact-desc">$82 saved per order</div>
                </div>
                <div class="impact-card">
                    <div class="impact-icon">
                        <svg class="icon-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="impact-title">Accuracy</div>
                    <div class="impact-value">99.2%</div>
                    <div class="impact-desc">Eliminates human errors</div>
                </div>
                <div class="impact-card">
                    <div class="impact-icon">
                        <svg class="icon-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="impact-title">Customer Satisfaction</div>
                    <div class="impact-value">+25%</div>
                    <div class="impact-desc">Faster response times</div>
                </div>
            </div>
        </section>
    </main>

    <script src="demo.js"></script>
</body>

</html>